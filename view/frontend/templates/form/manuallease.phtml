<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @var $block \Webguru\ManualLease\Block\Form\Manuallease
 * @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer
 */
$methodCode = $block->escapeHtml($block->getMethodCode());
?>
<fieldset class="fieldset items <?= /* @noEscape */ $methodCode ?>"
          id="payment_form_<?= /* @noEscape */ $methodCode ?>">
    <div class="field number required">
        <label for="lease_number" class="label"><span><?= $block->escapeHtml(__('Lease Number')) ?></span></label>
        <div class="control">
            <input type="text" id="lease_number" name="payment[lease_number]"
                   title="<?= $block->escapeHtml(__('Lease Number')) ?>"
                   class="input-text required-entry"
                   value="<?= $block->escapeHtml($block->getInfoData('lease_number')) ?>" />
        </div>
    </div>
</fieldset>
<?= /* @noEscape */ $secureRenderer->renderStyleAsTag(
    "display:none",
    'fieldset#payment_form_' . /* @noEscape */ $methodCode
) ?>
