<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="payment-method" data-bind="css: {'_active': (getCode() == isChecked())}">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()"/>
        <label data-bind="attr: {'for': getCode()}" class="label">
            <span data-bind="text: getTitle()"></span>
        </label>
    </div>

    <div class="payment-method-content">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <div class="payment-method-billing-address">
            <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>
        <form id="manuallease-form" class="form form-manual-lease" data-role="manuallease-form">
            <fieldset class="fieldset payment method" data-bind='attr: {id: "payment_form_" + getCode()}'>
                <div class="field field-number required">
                    <label for="lease_number" class="label">
                        <span><!-- ko i18n: 'Lease Number'--><!-- /ko --></span>
                    </label>
                    <div class="control">
                        <input type="text"
                               id="lease_number"
                               name="payment[lease_number]"
                               data-validate="{required:true}"
                               data-bind='
                                attr: {title: $t("Lease Number")},
                                value: leaseNumber'
                               class="input-text"/>
                    </div>
                </div>
            </fieldset>

            <div class="checkout-agreements-block">
                <!-- ko foreach: $parent.getRegion('before-place-order') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                <!--/ko-->
            </div>

            <div class="actions-toolbar" id="review-buttons-container">
                <div class="primary">
                    <button class="action primary checkout"
                            type="submit"
                            data-bind="
                            click: placeOrder,
                            attr: {title: $t('Place Order')},
                            enable: (getCode() == isChecked()),
                            css: {disabled: !isPlaceOrderActionAllowed()}
                            "
                            data-role="review-save">
                        <span data-bind="i18n: 'Place Order'"></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

