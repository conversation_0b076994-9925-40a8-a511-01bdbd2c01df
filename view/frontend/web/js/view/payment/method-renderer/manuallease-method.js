/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/* @api */
define([
    'Magento_Checkout/js/view/payment/default',
    'jquery',
    'mage/validation'
], function (Component, $) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Webguru_ManualLease/payment/manuallease-form',
            leaseNumber: ''
        },

        /** @inheritdoc */
        initObservable: function () {
            this._super()
                .observe('leaseNumber');

            return this;
        },

        /**
         * @return {Object}
         */
        getData: function () {
            return {
                method: this.item.method,
                'lease_number': this.leaseNumber(),
                'additional_data': null
            };
        },

        /**
         * @return {jQuery}
         */
        validate: function () {
            var form = 'form[data-role=manuallease-form]';

            return $(form).validation() && $(form).validation('isValid');
        }
    });
});
