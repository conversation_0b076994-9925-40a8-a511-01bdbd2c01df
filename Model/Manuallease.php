<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Webguru\ManualLease\Model;

use Magento\Framework\Exception\LocalizedException;

/**
 * Class Manuallease
 *
 * Update additional payments fields and validate the payment data
 * @method \Magento\Quote\Api\Data\PaymentMethodExtensionInterface getExtensionAttributes()
 *
 * @api
 * @since 100.0.2
 */
class Manuallease extends \Magento\Payment\Model\Method\AbstractMethod
{
    public const PAYMENT_METHOD_MANUALLEASE_CODE = 'manuallease';

    /**
     * @var string
     */
    protected $_code = self::PAYMENT_METHOD_MANUALLEASE_CODE;

    /**
     * @var string
     */
    protected $_formBlockType = \Webguru\ManualLease\Block\Form\Manuallease::class;

    /**
     * @var string
     */
    protected $_infoBlockType = \Webguru\ManualLease\Block\Info\Manuallease::class;

    /**
     * @var bool
     */
    protected $_isOffline = true;

    /**
     * Assign data to info model instance
     *
     * @param \Magento\Framework\DataObject|mixed $data
     * @return $this
     * @throws LocalizedException
     */
    public function assignData(\Magento\Framework\DataObject $data)
    {
        $this->getInfoInstance()->setLeaseNumber($data->getLeaseNumber());
        return $this;
    }

    /**
     * Validate payment method information object
     *
     * @return $this
     * @throws LocalizedException
     * @since 100.2.3
     */
    public function validate()
    {
        parent::validate();

        return $this;
    }
}
