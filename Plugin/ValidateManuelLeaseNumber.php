<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Webguru\ManualLease\Plugin;

use Magento\Framework\Exception\LocalizedException;
use Webguru\ManualLease\Model\Manuallease;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\QuoteManagement;

/**
 * Class ValidateManualLeaseNumber
 *
 * Validate manual lease number before submit order
 */
class ValidateManualLeaseNumber
{
    /**
     * Before submitOrder plugin.
     *
     * @param QuoteManagement $subject
     * @param Quote $quote
     * @param array $orderData
     * @return void
     * @throws LocalizedException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeSubmit(
        QuoteManagement $subject,
        Quote $quote,
        array $orderData = []
    ): void {
        $payment = $quote->getPayment();
        if ($payment->getMethod() === Manuallease::PAYMENT_METHOD_PURCHASEORDER_CODE
            && empty($payment->getPoNumber())) {
            throw new LocalizedException(__('Purchase order number is a required field.'));
        }
    }
}
